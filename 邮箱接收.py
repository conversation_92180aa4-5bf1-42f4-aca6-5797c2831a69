import poplib
import email
import re
from bs4 import BeautifulSoup
import time
import os
import queue
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime
import json
import base64

# 使用绝对路径确保文件位置统一
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_FILE = os.path.join(SCRIPT_DIR, "email_config.json")

# --- 全局配置 ---
POP3_SERVER = 'pop.2925.com'
POP3_PORT = 110
# 监控时每次刷新的间隔时间（秒）
REFRESH_INTERVAL_SECONDS = 1

def save_config(email, password, remember_password, auto_copy=True):
    """保存配置到文件"""
    config = {
        "email": email,
        "password": base64.b64encode(password.encode()).decode() if remember_password else "",
        "remember_password": remember_password,
        "auto_copy": auto_copy
    }
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        print(f"保存配置失败: {e}")

def load_config():
    """从文件加载配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 解码密码
                if config.get("password"):
                    config["password"] = base64.b64decode(config["password"]).decode()
                return config
    except Exception as e:
        print(f"加载配置失败: {e}")

    return {"email": "", "password": "", "remember_password": False, "auto_copy": True}

def decode_payload(payload, charset):
    """安全地用给定字符集解码负载。"""
    try:
        return payload.decode(charset)
    except (UnicodeDecodeError, LookupError):
        # 回退到gbk以处理常见中文邮件，忽略错误
        return payload.decode('gbk', errors='ignore')

def get_clean_body_from_msg(msg):
    """解析一个email.message对象并返回纯净的文本正文。"""
    body_content, html_content = "", ""
    if msg.is_multipart():
        for part in msg.walk():
            # 跳过附件
            if part.get('Content-Disposition', '').startswith('attachment'):
                continue
            payload = part.get_payload(decode=True)
            if not payload:
                continue
            charset = part.get_content_charset() or 'utf-8'
            content_type = part.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)
    else:  # 单部分邮件
        if not msg.get('Content-Disposition', '').startswith('attachment'):
            payload = msg.get_payload(decode=True)
            charset = msg.get_content_charset() or 'utf-8'
            content_type = msg.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)
    if not body_content.strip() and html_content:
        soup = BeautifulSoup(html_content, 'lxml')
        return soup.get_text(separator='\n', strip=True)
    return body_content

def find_code_in_text(body_text):
    """使用正则表达式在字符串中查找6位验证码。"""
    # 匹配格式: 123456, 123 456, 1 2 3 4 5 6
    patterns = [r'\b\d{6}\b', r'\b\d{3}\s\d{3}\b', r'\b(?:\d\s){5}\d\b']
    for pattern in patterns:
        match = re.search(pattern, body_text)
        if match:
            # 返回去除了空格的验证码
            return match.group(0).replace(" ", "")
    return None

def establish_baseline(server, output_queue):
    """获取当前所有邮件的UIDL，建立基线。"""
    try:
        resp, uid_lines, octets = server.uidl()
        # 将UIDL列表转换为一个UID集合，用于快速查找
        seen_uids = {line.split()[1] for line in uid_lines}
        output_queue.put(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
        return seen_uids
    except Exception as e:
        output_queue.put(f"建立基线时出错: {e}")
        return None

def main(email_account, email_password, output_queue):
    """主执行逻辑：建立基线，然后持续监控新邮件。"""
    output_queue.put(f"正在监控邮箱: {email_account}")
    
    # --- 1. 首次连接并建立基线 ---
    try:
        server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
        server.user(email_account)
        server.pass_(email_password)
        output_queue.put("验证成功。")
        seen_uids = establish_baseline(server, output_queue)
        server.quit()
        if seen_uids is None:
            return
    except poplib.error_proto as e:
        output_queue.put(f"错误：登录失败。请检查凭据。 ({e})")
        return
    except Exception as e:
        output_queue.put(f"连接或建立基线时发生未知错误: {e}")
        return

    # --- 2. 进入监控循环 ---
    loop_counter = 0
    while True:
        try:
            # 添加一个停止标志的检查
            try:
                if output_queue.get_nowait() == 'STOP':
                    break
            except queue.Empty:
                pass

            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
            server.user(email_account)
            server.pass_(email_password)
            
            resp, uid_lines, octets = server.uidl()
            
            # 创建当前邮件UID到消息号的映射
            current_uid_map = {
                parts[1]: parts[0]
                for line in uid_lines
                if len(parts := line.split()) == 2
            }

            # 找出新的UID
            new_uids = set(current_uid_map.keys()) - seen_uids
            
            if new_uids:
                loop_counter = 0 # 重置计数器
                output_queue.put(f"\n发现 {len(new_uids)} 封新邮件，正在检查...")
                # 将新邮件按消息号排序，确保先检查最新的
                new_messages = sorted(
                    [(int(current_uid_map[uid]), uid) for uid in new_uids],
                    key=lambda x: x[0],
                    reverse=True
                )

                for msg_num, uid in new_messages:
                    resp, lines, octets = server.retr(msg_num)
                    msg_content = b'\r\n'.join(lines)
                    msg = email.message_from_bytes(msg_content)
                    
                    body = get_clean_body_from_msg(msg)
                    code = find_code_in_text(body)
                    
                    if code:
                        # 打印验证码给用户看
                        output_queue.put(f"成功提取到新邮件中的验证码: {code}")
                        # 打印一个特殊格式的字符串，用于进程间通信
                        output_queue.put(f"VERIFICATION_CODE:{code}")
                        
                        server.quit()
                        return # 任务完成，退出程序
                
                # 如果检查完所有新邮件都没找到验证码，则更新基线
                output_queue.put("新邮件中未发现验证码，将继续监控...")
                seen_uids.update(new_uids)
            else:
                loop_counter += 1
                # 每 15 秒打印一次状态，避免刷屏
                if loop_counter % 15 == 1:
                    output_queue.put(f"没有新邮件，继续监控... ({time.strftime('%H:%M:%S')})")


            server.quit()
            time.sleep(REFRESH_INTERVAL_SECONDS)

        except KeyboardInterrupt:
            output_queue.put("\n程序已手动停止。")
            break
        except Exception as e:
            output_queue.put(f"\n监控循环中发生错误: {e}。等待10秒后重试...")
            time.sleep(10)

class EmailMonitorGUI:
    """邮箱监控的图形用户界面"""

    def __init__(self, root):
        self.root = root
        self.root.title("邮箱验证码监控器")
        self.root.geometry("600x550")
        self.root.resizable(True, True)

        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.output_queue = queue.Queue()

        # 加载配置
        self.config = load_config()

        self.setup_ui()
        self.load_saved_config()
        self.check_queue()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)

        # 邮箱账号输入
        ttk.Label(main_frame, text="邮箱账号:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(main_frame, textvariable=self.email_var, width=40)
        self.email_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 邮箱密码输入
        ttk.Label(main_frame, text="邮箱密码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=40)
        self.password_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 选项框架
        options_frame = ttk.Frame(main_frame)
        options_frame.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # 记住密码选项
        self.remember_var = tk.BooleanVar()
        self.remember_check = ttk.Checkbutton(options_frame, text="记住密码", variable=self.remember_var)
        self.remember_check.pack(side=tk.LEFT)

        # 自动复制选项
        self.auto_copy_var = tk.BooleanVar(value=True)  # 默认开启
        self.auto_copy_check = ttk.Checkbutton(options_frame, text="自动复制验证码", variable=self.auto_copy_var)
        self.auto_copy_check.pack(side=tk.LEFT, padx=(20, 0))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)

        # 开始/停止按钮
        self.start_button = ttk.Button(button_frame, text="开始监控", command=self.toggle_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=5)

        # 清空日志按钮
        self.clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT, padx=5)

        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        self.status_label.grid(row=4, column=0, columnspan=2, pady=5)

        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="监控日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 验证码显示区域
        result_frame = ttk.LabelFrame(main_frame, text="验证码结果", padding="5")
        result_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        result_frame.columnconfigure(1, weight=1)

        ttk.Label(result_frame, text="验证码:").grid(row=0, column=0, sticky=tk.W)
        self.code_var = tk.StringVar()
        self.code_entry = ttk.Entry(result_frame, textvariable=self.code_var, font=("Arial", 14, "bold"))
        self.code_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))

        # 复制按钮
        self.copy_button = ttk.Button(result_frame, text="复制", command=self.copy_code)
        self.copy_button.grid(row=0, column=2, padx=(10, 0))

    def load_saved_config(self):
        """加载保存的配置"""
        self.email_var.set(self.config.get("email", ""))
        if self.config.get("remember_password", False):
            self.password_var.set(self.config.get("password", ""))
            self.remember_var.set(True)
        # 加载自动复制设置
        self.auto_copy_var.set(self.config.get("auto_copy", True))

    def save_current_config(self):
        """保存当前配置"""
        email = self.email_var.get().strip()
        password = self.password_var.get().strip()
        remember = self.remember_var.get()
        auto_copy = self.auto_copy_var.get()

        if email:  # 只有当邮箱不为空时才保存
            save_config(email, password if remember else "", remember, auto_copy)

    def log_message(self, message):
        """在日志区域添加消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)

        # 如果是验证码消息，提取并显示
        if message.startswith("VERIFICATION_CODE:"):
            code = message.split(":", 1)[1]
            self.code_var.set(code)

            # 自动复制到剪贴板
            if self.auto_copy_var.get():
                try:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(code)
                    self.status_var.set(f"验证码已获取并复制到剪贴板: {code}")
                    # 添加复制成功的日志消息
                    copy_timestamp = datetime.now().strftime("%H:%M:%S")
                    copy_message = f"[{copy_timestamp}] 验证码已自动复制到剪贴板\n"
                    self.log_text.insert(tk.END, copy_message)
                    self.log_text.see(tk.END)
                except Exception as e:
                    self.status_var.set(f"验证码已获取: {code} (复制失败)")
                    error_timestamp = datetime.now().strftime("%H:%M:%S")
                    error_message = f"[{error_timestamp}] 自动复制失败: {e}\n"
                    self.log_text.insert(tk.END, error_message)
                    self.log_text.see(tk.END)
            else:
                self.status_var.set(f"验证码已获取: {code}")

            # 自动停止监控
            self.stop_monitoring()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.code_var.set("")

    def copy_code(self):
        """复制验证码到剪贴板"""
        code = self.code_var.get()
        if code:
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            messagebox.showinfo("提示", "验证码已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有验证码可复制！")

    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.monitoring:
            self.start_monitoring()
        else:
            self.stop_monitoring()

    def start_monitoring(self):
        """开始监控"""
        email_account = self.email_var.get().strip()
        email_password = self.password_var.get().strip()

        if not email_account or not email_password:
            messagebox.showerror("错误", "请输入邮箱账号和密码！")
            return

        # 保存配置
        self.save_current_config()

        self.monitoring = True
        self.start_button.config(text="停止监控", state="normal")
        self.email_entry.config(state="disabled")
        self.password_entry.config(state="disabled")
        self.remember_check.config(state="disabled")
        self.status_var.set("正在启动监控...")
        self.code_var.set("")

        # 在新线程中运行监控
        self.monitor_thread = threading.Thread(
            target=main,
            args=(email_account, email_password, self.output_queue),
            daemon=True
        )
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            # 发送停止信号
            self.output_queue.put('STOP')

            self.start_button.config(text="开始监控")
            self.email_entry.config(state="normal")
            self.password_entry.config(state="normal")
            self.remember_check.config(state="normal")
            self.status_var.set("监控已停止")

    def check_queue(self):
        """检查队列中的消息"""
        try:
            while True:
                message = self.output_queue.get_nowait()
                if message == 'STOP':
                    break
                self.log_message(message)
        except queue.Empty:
            pass

        # 每100毫秒检查一次队列
        self.root.after(100, self.check_queue)

    def on_closing(self):
        """窗口关闭时的处理"""
        if self.monitoring:
            self.stop_monitoring()
        self.root.destroy()


def run_gui():
    """运行图形界面"""
    root = tk.Tk()
    app = EmailMonitorGUI(root)

    # 设置窗口关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # 设置窗口图标（如果有的话）
    try:
        # 可以在这里设置窗口图标
        pass
    except:
        pass

    root.mainloop()


if __name__ == "__main__":
    # 启动图形界面
    print("启动邮箱验证码监控器...")
    run_gui()