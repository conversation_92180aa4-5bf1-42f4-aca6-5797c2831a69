@echo off
echo ========================================
echo 📦 安装邮箱监控器打包依赖
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python: https://www.python.org/
    pause
    exit /b 1
)

echo.
echo 📥 安装PyInstaller...
pip install pyinstaller

echo.
echo 📥 安装其他依赖...
pip install beautifulsoup4 lxml

echo.
echo ✅ 依赖安装完成!
echo 现在可以运行 python 打包.py 来打包应用
echo.
pause
