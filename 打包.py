#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱验证码监控器打包脚本
使用PyInstaller将Python脚本打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 配置信息
APP_NAME = "邮箱验证码监控器"
SCRIPT_NAME = "邮箱接收.py"
ICON_FILE = "image.png"
OUTPUT_DIR = "dist"
BUILD_DIR = "build"

def check_requirements():
    """检查必要的文件和依赖"""
    print("🔍 检查打包环境...")

    # 检查主脚本文件
    if not os.path.exists(SCRIPT_NAME):
        print(f"❌ 错误: 找不到主脚本文件 {SCRIPT_NAME}")
        return False

    # 检查图标文件
    if not os.path.exists(ICON_FILE):
        print(f"⚠️  警告: 找不到图标文件 {ICON_FILE}")
        print("   将使用默认图标")

    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 已安装: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 错误: PyInstaller 未安装")
        print("   请运行: pip install pyinstaller")
        return False

    return True

def clean_build_files():
    """清理之前的构建文件"""
    print("🧹 清理构建文件...")

    dirs_to_clean = [OUTPUT_DIR, BUILD_DIR, "__pycache__"]
    files_to_clean = [f"{APP_NAME}.spec"]

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")

    for file_name in files_to_clean:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"   删除文件: {file_name}")

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("📝 创建打包配置文件...")

    # 检查是否有图标文件
    icon_option = f"icon='{ICON_FILE}'" if os.path.exists(ICON_FILE) else "icon=None"

    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{SCRIPT_NAME}'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'tkinter.messagebox',
        'poplib',
        'email',
        'email.message',
        'bs4',
        'lxml',
        'threading',
        'queue',
        'json',
        'base64',
        'datetime',
        're',
        'time',
        'os'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{APP_NAME}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    {icon_option},
    version_file=None,
)
'''

    spec_filename = f"{APP_NAME}.spec"
    with open(spec_filename, 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print(f"   创建配置文件: {spec_filename}")
    return spec_filename

def build_executable(spec_file):
    """使用PyInstaller构建可执行文件"""
    print("🔨 开始打包...")

    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不询问覆盖
        spec_file
    ]

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败!")
        print(f"错误信息: {e.stderr}")
        return False

def post_build_cleanup():
    """打包后清理"""
    print("🧹 清理临时文件...")

    # 删除build目录
    if os.path.exists(BUILD_DIR):
        shutil.rmtree(BUILD_DIR)
        print(f"   删除目录: {BUILD_DIR}")

    # 删除spec文件
    spec_file = f"{APP_NAME}.spec"
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print(f"   删除文件: {spec_file}")

def show_results():
    """显示打包结果"""
    exe_path = os.path.join(OUTPUT_DIR, f"{APP_NAME}.exe")

    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"\n🎉 打包完成!")
        print(f"📁 输出目录: {os.path.abspath(OUTPUT_DIR)}")
        print(f"📄 可执行文件: {APP_NAME}.exe")
        print(f"📏 文件大小: {file_size:.1f} MB")
        print(f"\n💡 使用方法:")
        print(f"   双击运行: {exe_path}")
        print(f"   或者在命令行中运行: {os.path.abspath(exe_path)}")
    else:
        print("\n❌ 打包失败: 找不到生成的可执行文件")

def main():
    """主函数"""
    print("=" * 50)
    print(f"📦 {APP_NAME} 打包工具")
    print("=" * 50)

    # 检查环境
    if not check_requirements():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return False

    try:
        # 清理旧文件
        clean_build_files()

        # 创建配置文件
        spec_file = create_spec_file()

        # 执行打包
        if build_executable(spec_file):
            # 清理临时文件
            post_build_cleanup()

            # 显示结果
            show_results()
            return True
        else:
            return False

    except KeyboardInterrupt:
        print("\n\n⏹️  用户取消打包")
        return False
    except Exception as e:
        print(f"\n❌ 打包过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()

    print("\n" + "=" * 50)
    if success:
        print("🎊 打包完成! 可以分发你的应用了!")
    else:
        print("💥 打包失败! 请检查错误信息")
    print("=" * 50)

    # 在Windows上暂停，让用户看到结果
    if os.name == 'nt':
        input("\n按回车键退出...")

    sys.exit(0 if success else 1)