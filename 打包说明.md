# 📦 邮箱验证码监控器打包说明

## 🎯 打包目标
将Python脚本打包成独立的Windows可执行文件(.exe)，无需安装Python环境即可运行。

## 📋 文件说明

- `邮箱接收.py` - 主程序文件
- `image.png` - 应用程序图标
- `打包.py` - 自动化打包脚本
- `安装依赖.bat` - 依赖安装脚本

## 🚀 打包步骤

### 1. 安装依赖
双击运行 `安装依赖.bat` 或手动执行：
```bash
pip install pyinstaller beautifulsoup4 lxml
```

### 2. 执行打包
运行打包脚本：
```bash
python 打包.py
```

### 3. 获取结果
打包完成后，在 `dist` 目录下找到：
- `邮箱验证码监控器.exe` - 可执行文件

## 📁 打包后的文件结构
```
dist/
└── 邮箱验证码监控器.exe  (约30-50MB)
```

## ✨ 打包特性

- ✅ 单文件打包 - 所有依赖都包含在一个exe文件中
- ✅ 无控制台窗口 - 纯GUI应用
- ✅ 自定义图标 - 使用 image.png 作为程序图标
- ✅ 自动清理 - 打包完成后自动清理临时文件
- ✅ 错误处理 - 完善的错误提示和处理

## 🎮 使用方法

打包完成后：
1. 将 `邮箱验证码监控器.exe` 复制到任意位置
2. 双击运行即可使用
3. 无需安装Python或任何依赖

## 🔧 自定义配置

在 `打包.py` 中可以修改：
- `APP_NAME` - 应用程序名称
- `ICON_FILE` - 图标文件路径
- 其他PyInstaller选项

## 📝 注意事项

1. 首次打包可能需要较长时间（5-10分钟）
2. 生成的exe文件较大（30-50MB）是正常的
3. 杀毒软件可能误报，需要添加信任
4. 建议在干净的环境中打包以减少文件大小

## 🐛 常见问题

**Q: 打包失败怎么办？**
A: 检查是否安装了所有依赖，确保Python环境正常

**Q: exe文件太大怎么办？**
A: 这是PyInstaller的特性，包含了完整的Python运行时

**Q: 杀毒软件报毒怎么办？**
A: 这是误报，可以添加到信任列表或使用代码签名

**Q: 在其他电脑上无法运行？**
A: 确保目标电脑是Windows系统，且没有被杀毒软件阻止
